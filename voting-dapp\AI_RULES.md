# AI Development Rules for Decentralized Voting App

This document outlines the core technologies and guidelines for developing this decentralized voting application.

## Tech Stack Overview

*   **Frontend Framework**: React (version 17.x) for building the user interface, ensuring a dynamic and responsive experience.
*   **Blockchain Interaction**: Web3.js for seamless communication with Ethereum-compatible blockchains (like Ganache), enabling smart contract interactions.
*   **Smart Contract Development**: Solidity for writing secure and robust smart contracts, managed and deployed using Truffle.
*   **Styling**: Tailwind CSS for a utility-first approach to styling, ensuring a consistent and responsive design across the application.
*   **UI Components**: Utilizes `shadcn/ui` for pre-built, accessible, and customizable UI components, enhancing development speed and consistency.
*   **Icons**: `lucide-react` for a comprehensive set of customizable SVG icons.
*   **Camera Integration**: `react-webcam` for capturing user photos, primarily for voter authentication.
*   **Decentralized Storage**: Integration with IPFS (InterPlanetary File System) for secure and decentralized storage of voter authentication images.
*   **Testing**: Jest and React Testing Library for frontend unit and integration tests, complemented by <PERSON><PERSON> and <PERSON><PERSON><PERSON> Assertions for thorough smart contract testing.
*   **Language**: Primarily JavaScript for existing frontend code, with a strong preference for TypeScript (`.tsx`) for all new components and files to improve code quality and maintainability.

## Library Usage Rules

*   **React**: Use for all UI components and application logic. New components should be created as TypeScript (`.tsx`) files.
*   **React Router**: Implement client-side routing using React Router. All main application routes should be defined within `src/App.tsx` (or `src/App.js` for now, with a plan to migrate to `.tsx`).
*   **Tailwind CSS**: All styling must be implemented using Tailwind CSS utility classes. Avoid creating custom CSS files unless absolutely necessary for global styles (e.g., font imports in `index.css`).
*   **shadcn/ui**: Prioritize using components from the `shadcn/ui` library for all new UI elements. If a specific `shadcn/ui` component doesn't meet requirements, create a new, small, and focused custom component.
*   **lucide-react**: Use this library for all icons throughout the application.
*   **Web3.js**: This is the sole library for all interactions with the Ethereum blockchain, including sending transactions, reading contract data, and managing accounts.
*   **Truffle**: Use for compiling, deploying, and testing all Solidity smart contracts.
*   **Chai & Truffle Assertions**: These libraries are mandatory for writing comprehensive and effective tests for smart contract functionality.
*   **react-webcam**: This library should be used for any features requiring camera access and photo capture.
*   **IPFS (or similar)**: For any persistent storage of user-generated content, such as voter authentication photos, integrate with IPFS or another decentralized storage solution.
*   **File Structure**: Maintain a clear and organized file structure. Place main application views in `src/pages/` and reusable UI components in `src/components/`. All source code must reside within the `src` directory, and directory names must be lowercase.