import React, { useState, useEffect } from 'react';
import { loadModels } from './utils/faceDetection';
import VoteForm from './VoteForm';
import CandidatesTable from './components/CandidatesTable';
import './App.css';
import getWeb3 from './getWeb3';
import Election from './contracts/Election.json';

function App() {
  const [candidates, setCandidates] = useState([]);
  const [modelsReady, setModelsReady] = useState(false);
  const [contract, setContract] = useState(null);
  const [account, setAccount] = useState('');

  useEffect(() => {
    const loadWeb3AndContract = async () => {
      const web3 = await getWeb3();
      const accounts = await web3.eth.getAccounts();
      setAccount(accounts[0]);
      const networkId = await web3.eth.net.getId();
      const deployedNetwork = Election.networks[networkId];
      const contract = new web3.eth.Contract(
        Election.abi,
        deployedNetwork.address
      );
      setContract(contract);
    };

    loadWeb3AndContract();
  }, []);

  useEffect(() => {
    const initialize = async () => {
      try {
        await loadModels();
        setModelsReady(true);
        console.log('Face detection models loaded successfully');
      } catch (error) {
        console.error('Failed to load models:', error);
      }
    };
    initialize();
  }, []);

  const handleVote = (candidateId) => {
    if (!contract || !account) {
      console.error('Contract or account not loaded');
      return;
    }

    contract.methods.vote(candidateId).send({ from: account })
      .then((receipt) => {
        console.log('Vote submitted:', receipt);
      })
      .catch((error) => {
        console.error('Error voting:', error);
      });
  };

  return (
    <div className="app-container">
      <h1>Ứng Dụng Bỏ Phiếu</h1>
      <VoteForm onVote={handleVote} />
      <CandidatesTable candidates={candidates} />
    </div>
  );
}

export default App;
