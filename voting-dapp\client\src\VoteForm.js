import React, { useState } from 'react';
import { Button } from "../components/ui/button";

export default function VoteForm({ onVote }) {
  const [selected, setSelected] = useState('');
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selected) {
      alert('Vui lòng chọn ứng viên');
      return;
    }
    onVote(Number(selected));
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2>Chọn ứng viên:</h2>
      <select 
        value={selected}
        onChange={(e) => setSelected(e.target.value)}
      >
        <option value="">-- Chọn --</option>
        <option value="1">Ứng viên 1</option>
        <option value="2">Ứng viên 2</option>
      </select>
      <Button type="submit">Bỏ phiếu</Button>
    </form>
  );
}
