// ... (phần import giữ nguyên)

const CameraCapture = ({ onPhotoCapture, existingDescriptors = [] }) => {
  // ... (phần khai báo state giữ nguyên)

  const capture = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Kiểm tra webcamRef và existingDescriptors
      if (!webcamRef.current) {
        throw new Error('Camera chưa sẵn sàng');
      }

      // Đảm bảo existingDescriptors luôn là mảng
      const descriptors = Array.isArray(existingDescriptors) ? existingDescriptors : [];
      
      const imageSrc = webcamRef.current.getScreenshot();
      const img = new Image();
      img.src = imageSrc;
      
      await new Promise((resolve) => img.onload = resolve);

      const descriptor = await detectAndExtractFace(img);
      
      // Kiểm tra trùng lặp
      if (descriptors.length > 0) {
        for (const existing of descriptors) {
          const isSame = await compareFaces(descriptor, existing);
          if (isSame) throw new Error('K<PERSON>ôn mặt đã được sử dụng');
        }
      }

      setCapturedImage(imageSrc);
      setFaceError(null);
      onPhotoCapture(imageSrc, descriptor);
    } catch (error) {
      setFaceError(error.message);
      console.error("Lỗi nhận dạng:", error);
    } finally {
      setIsLoading(false);
    }
  }, [webcamRef, existingDescriptors, onPhotoCapture]);

  // ... (phần còn lại giữ nguyên)
};