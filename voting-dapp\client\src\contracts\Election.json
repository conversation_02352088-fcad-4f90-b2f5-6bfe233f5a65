{"contractName": "Election", "abi": [{"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "candidateId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "CandidateAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "candidateId", "type": "uint256"}], "name": "Su<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"constant": true, "inputs": [], "name": "candidateCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "candidates", "outputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "voteCount", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "voters", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "string", "name": "_name", "type": "string"}], "name": "addCandidate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "_candidateId", "type": "uint256"}], "name": "vote", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "getAllCandidates", "outputs": [{"components": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "voteCount", "type": "uint256"}], "internalType": "struct Election.Candidate[]", "name": "", "type": "tuple[]"}], "payable": false, "stateMutability": "view", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.5.16+commit.9c3226ce\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"candidateId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"}],\"name\":\"CandidateAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"candidateId\",\"type\":\"uint256\"}],\"name\":\"Su<PERSON><PERSON>BoPhieu\",\"type\":\"event\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"string\",\"name\":\"_name\",\"type\":\"string\"}],\"name\":\"addCandidate\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"candidateCount\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"candidates\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"voteCount\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"getAllCandidates\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"id\",\"type\":\"uint256\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"uint256\",\"name\":\"voteCount\",\"type\":\"uint256\"}],\"internalType\":\"struct Election.Candidate[]\",\"name\":\"\",\"type\":\"tuple[]\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_candidateId\",\"type\":\"uint256\"}],\"name\":\"vote\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"voters\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"project:/contracts/Election.sol\":\"Election\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/Election.sol\":{\"keccak256\":\"0x2bb349b235a3e0a43a7bc349c5d62c27a7486a2aeacb106953e461e73a2133dc\",\"urls\":[\"bzz-raw://412efbc3c9819a016b7e2f4449065c2928bd34f25cc48dee943834f33b512cf8\",\"dweb:/ipfs/QmZyQDD7uNf858BRTLmungTLRS9ZyVpGAUf7gnGwugeAN4\"]}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "sourceMap": "71:2702:0:-;;;1340:187;8:9:-1;5:2;;;30:1;27;20:12;5:2;1340:187:0;1380:10;1372:5;;:18;;;;;;;;;;;;;;;;;;1440:34;;;;;;;;;;;;;;;;;;:12;;;:34;;:::i;:::-;1485;;;;;;;;;;;;;;;;;;:12;;;:34;;:::i;:::-;71:2702;;1615:223;1029:5;;;;;;;;;;;1015:19;;:10;:19;;;1007:87;;;;;;;;;;;;;;;;;;;;;;1686:14;;:16;;;;;;;;;;;;;1742:35;;;;;;;;1752:14;;1742:35;;;;1768:5;1742:35;;;;1775:1;1742:35;;;1713:10;:26;1724:14;;1713:26;;;;;;;;;;;:64;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;1808:14;;1793:37;1824:5;1793:37;;;;;;;;;;;;;;;1615:223;:::o;71:2702::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;5:347:-1:-;;117:39;150:5;117:39;;;168:71;232:6;227:3;168:71;;;161:78;;244:52;289:6;284:3;277:4;270:5;266:16;244:52;;;317:29;339:6;317:29;;;312:3;308:39;301:46;;97:255;;;;;;360:465;;520:67;584:2;579:3;520:67;;;513:74;;620:66;616:1;611:3;607:11;600:87;721:66;716:2;711:3;707:12;700:88;816:2;811:3;807:12;800:19;;506:319;;;;833:301;;971:2;960:9;956:18;948:26;;1021:9;1015:4;1011:20;1007:1;996:9;992:17;985:47;1046:78;1119:4;1110:6;1046:78;;;1038:86;;942:192;;;;;1141:407;;1332:2;1321:9;1317:18;1309:26;;1382:9;1376:4;1372:20;1368:1;1357:9;1353:17;1346:47;1407:131;1533:4;1407:131;;;1399:139;;1303:245;;;;1555:122;;1649:5;1643:12;1633:22;;1614:63;;;;1685:163;;1800:6;1795:3;1788:19;1837:4;1832:3;1828:14;1813:29;;1781:67;;;;;1857:268;1922:1;1929:101;1943:6;1940:1;1937:13;1929:101;;;2019:1;2014:3;2010:11;2004:18;2000:1;1995:3;1991:11;1984:39;1965:2;1962:1;1958:10;1953:15;;1929:101;;;2045:6;2042:1;2039:13;2036:2;;;2110:1;2101:6;2096:3;2092:16;2085:27;2036:2;1906:219;;;;;2133:97;;2221:2;2217:7;2212:2;2205:5;2201:14;2197:28;2187:38;;2181:49;;;;71:2702:0;;;;;;;", "deployedSourceMap": "71:2702:0:-;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;71:2702:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;1872:533;;;;;;;;;;;;;;;;:::i;:::-;;2466:304;;;:::i;:::-;;;;;;;;;;;;;;;;436:47;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;1615:223;;;;;;;;;;;;;;;;:::i;:::-;;627:20;;;:::i;:::-;;;;;;;;;;;;;;;;342:38;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;531:29;;;:::i;:::-;;;;;;;;;;;;;;;;1872:533;1251:6;:18;1258:10;1251:18;;;;;;;;;;;;;;;;;;;;;;;;;1250:19;1242:70;;;;;;;;;;;;;;;;;;;;;;2021:1;2006:12;:16;:50;;;;;2042:14;;2026:12;:30;;2006:50;1998:101;;;;;;;;;;;;;;;;;;;;;;2189:4;2168:6;:18;2175:10;2168:18;;;;;;;;;;;;;;;;:25;;;;;;;;;;;;;;;;;;2266:10;:24;2277:12;2266:24;;;;;;;;;;;:34;;;:36;;;;;;;;;;;;;2384:12;2370:27;;;;;;;;;;1872:533;:::o;2466:304::-;2515:18;2546:32;2597:14;;2581:31;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;2546:66;;2628:9;2640:1;2628:13;;2623:109;2648:14;;2643:1;:19;2623:109;;2707:10;:13;2718:1;2707:13;;;;;;;;;;;2684:36;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:13;2702:1;2698;:5;2684:20;;;;;;;;;;;;;:36;;;;2664:3;;;;;;;2623:109;;;;2749:13;2742:20;;;2466:304;:::o;436:47::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;1615:223::-;1029:5;;;;;;;;;;;1015:19;;:10;:19;;;1007:87;;;;;;;;;;;;;;;;;;;;;;1686:14;;:16;;;;;;;;;;;;;1742:35;;;;;;;;1752:14;;1742:35;;;;1768:5;1742:35;;;;1775:1;1742:35;;;1713:10;:26;1724:14;;1713:26;;;;;;;;;;;:64;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;1808:14;;1793:37;1824:5;1793:37;;;;;;;;;;;;;;;1615:223;:::o;627:20::-;;;;;;;;;;;;;:::o;342:38::-;;;;;;;;;;;;;;;;;;;;;;:::o;531:29::-;;;;:::o;71:2702::-;;;;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;:::o;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;5:130:-1:-;;85:6;72:20;63:29;;97:33;124:5;97:33;;;57:78;;;;;143:442;;245:3;238:4;230:6;226:17;222:27;212:2;;263:1;260;253:12;212:2;300:6;287:20;322:65;337:49;379:6;337:49;;;322:65;;;313:74;;407:6;400:5;393:21;443:4;435:6;431:17;476:4;469:5;465:16;511:3;502:6;497:3;493:16;490:25;487:2;;;528:1;525;518:12;487:2;538:41;572:6;567:3;562;538:41;;;205:380;;;;;;;;593:130;;673:6;660:20;651:29;;685:33;712:5;685:33;;;645:78;;;;;730:241;;834:2;822:9;813:7;809:23;805:32;802:2;;;850:1;847;840:12;802:2;885:1;902:53;947:7;938:6;927:9;923:22;902:53;;;892:63;;864:97;796:175;;;;;978:347;;1092:2;1080:9;1071:7;1067:23;1063:32;1060:2;;;1108:1;1105;1098:12;1060:2;1171:1;1160:9;1156:17;1143:31;1194:18;1186:6;1183:30;1180:2;;;1226:1;1223;1216:12;1180:2;1246:63;1301:7;1292:6;1281:9;1277:22;1246:63;;;1236:73;;1122:193;1054:271;;;;;1332:241;;1436:2;1424:9;1415:7;1411:23;1407:32;1404:2;;;1452:1;1449;1442:12;1404:2;1487:1;1504:53;1549:7;1540:6;1529:9;1525:22;1504:53;;;1494:63;;1466:97;1398:175;;;;;1581:237;;1722:90;1808:3;1800:6;1722:90;;;1708:104;;1701:117;;;;;1826:113;1909:24;1927:5;1909:24;;;1904:3;1897:37;1891:48;;;2013:1008;;2198:74;2266:5;2198:74;;;2285:106;2384:6;2379:3;2285:106;;;2278:113;;2414:3;2456:4;2448:6;2444:17;2439:3;2435:27;2483:76;2553:5;2483:76;;;2579:7;2607:1;2592:390;2617:6;2614:1;2611:13;2592:390;;;2679:9;2673:4;2669:20;2664:3;2657:33;2724:6;2718:13;2746:104;2845:4;2830:13;2746:104;;;2738:112;;2867:80;2940:6;2867:80;;;2857:90;;2970:4;2965:3;2961:14;2954:21;;2649:333;2639:1;2636;2632:9;2627:14;;2592:390;;;2596:14;2995:4;2988:11;;3012:3;3005:10;;2177:844;;;;;;;;;;3029:104;3106:21;3121:5;3106:21;;;3101:3;3094:34;3088:45;;;3140:347;;3252:39;3285:5;3252:39;;;3303:71;3367:6;3362:3;3303:71;;;3296:78;;3379:52;3424:6;3419:3;3412:4;3405:5;3401:16;3379:52;;;3452:29;3474:6;3452:29;;;3447:3;3443:39;3436:46;;3232:255;;;;;;3494:319;;3592:35;3621:5;3592:35;;;3639:61;3693:6;3688:3;3639:61;;;3632:68;;3705:52;3750:6;3745:3;3738:4;3731:5;3727:16;3705:52;;;3778:29;3800:6;3778:29;;;3773:3;3769:39;3762:46;;3572:241;;;;;;3820:339;;3928:35;3957:5;3928:35;;;3975:71;4039:6;4034:3;3975:71;;;3968:78;;4051:52;4096:6;4091:3;4084:4;4077:5;4073:16;4051:52;;;4124:29;4146:6;4124:29;;;4119:3;4115:39;4108:46;;3908:251;;;;;;4167:465;;4327:67;4391:2;4386:3;4327:67;;;4320:74;;4427:66;4423:1;4418:3;4414:11;4407:87;4528:66;4523:2;4518:3;4514:12;4507:88;4623:2;4618:3;4614:12;4607:19;;4313:319;;;;4641:465;;4801:67;4865:2;4860:3;4801:67;;;4794:74;;4901:66;4897:1;4892:3;4888:11;4881:87;5002:66;4997:2;4992:3;4988:12;4981:88;5097:2;5092:3;5088:12;5081:19;;4787:319;;;;5115:465;;5275:67;5339:2;5334:3;5275:67;;;5268:74;;5375:66;5371:1;5366:3;5362:11;5355:87;5476:66;5471:2;5466:3;5462:12;5455:88;5571:2;5566:3;5562:12;5555:19;;5261:319;;;;5649:708;;5784:4;5779:3;5775:14;5865:4;5858:5;5854:16;5848:23;5877:63;5934:4;5929:3;5925:14;5911:12;5877:63;;;5804:142;6019:4;6012:5;6008:16;6002:23;6071:3;6065:4;6061:14;6054:4;6049:3;6045:14;6038:38;6091:69;6155:4;6141:12;6091:69;;;6083:77;;5956:216;6250:4;6243:5;6239:16;6233:23;6262:63;6319:4;6314:3;6310:14;6296:12;6262:63;;;6182:149;6348:4;6341:11;;5757:600;;;;;;6364:103;6437:24;6455:5;6437:24;;;6432:3;6425:37;6419:48;;;6474:113;6557:24;6575:5;6557:24;;;6552:3;6545:37;6539:48;;;6594:213;;6712:2;6701:9;6697:18;6689:26;;6726:71;6794:1;6783:9;6779:17;6770:6;6726:71;;;6683:124;;;;;6814:441;;7022:2;7011:9;7007:18;6999:26;;7072:9;7066:4;7062:20;7058:1;7047:9;7043:17;7036:47;7097:148;7240:4;7231:6;7097:148;;;7089:156;;6993:262;;;;;7262:201;;7374:2;7363:9;7359:18;7351:26;;7388:65;7450:1;7439:9;7435:17;7426:6;7388:65;;;7345:118;;;;;7470:301;;7608:2;7597:9;7593:18;7585:26;;7658:9;7652:4;7648:20;7644:1;7633:9;7629:17;7622:47;7683:78;7756:4;7747:6;7683:78;;;7675:86;;7579:192;;;;;7778:407;;7969:2;7958:9;7954:18;7946:26;;8019:9;8013:4;8009:20;8005:1;7994:9;7990:17;7983:47;8044:131;8170:4;8044:131;;;8036:139;;7940:245;;;;8192:407;;8383:2;8372:9;8368:18;8360:26;;8433:9;8427:4;8423:20;8419:1;8408:9;8404:17;8397:47;8458:131;8584:4;8458:131;;;8450:139;;8354:245;;;;8606:407;;8797:2;8786:9;8782:18;8774:26;;8847:9;8841:4;8837:20;8833:1;8822:9;8818:17;8811:47;8872:131;8998:4;8872:131;;;8864:139;;8768:245;;;;9020:213;;9138:2;9127:9;9123:18;9115:26;;9152:71;9220:1;9209:9;9205:17;9196:6;9152:71;;;9109:124;;;;;9240:515;;9430:2;9419:9;9415:18;9407:26;;9444:71;9512:1;9501:9;9497:17;9488:6;9444:71;;;9563:9;9557:4;9553:20;9548:2;9537:9;9533:18;9526:48;9588:74;9657:4;9648:6;9588:74;;;9580:82;;9673:72;9741:2;9730:9;9726:18;9717:6;9673:72;;;9401:354;;;;;;;9762:256;;9824:2;9818:9;9808:19;;9862:4;9854:6;9850:17;9961:6;9949:10;9946:22;9925:18;9913:10;9910:34;9907:62;9904:2;;;9982:1;9979;9972:12;9904:2;10002:10;9998:2;9991:22;9802:216;;;;;10025:322;;10169:18;10161:6;10158:30;10155:2;;;10201:1;10198;10191:12;10155:2;10268:4;10264:9;10257:4;10249:6;10245:17;10241:33;10233:41;;10332:4;10326;10322:15;10314:23;;10092:255;;;;10354:171;;10460:3;10452:11;;10498:4;10493:3;10489:14;10481:22;;10446:79;;;;10532:157;;10661:5;10655:12;10645:22;;10626:63;;;;10696:118;;10786:5;10780:12;10770:22;;10751:63;;;;10821:122;;10915:5;10909:12;10899:22;;10880:63;;;;10950:128;;11068:4;11063:3;11059:14;11051:22;;11045:33;;;;11086:198;;11236:6;11231:3;11224:19;11273:4;11268:3;11264:14;11249:29;;11217:67;;;;;11293:153;;11398:6;11393:3;11386:19;11435:4;11430:3;11426:14;11411:29;;11379:67;;;;;11455:163;;11570:6;11565:3;11558:19;11607:4;11602:3;11598:14;11583:29;;11551:67;;;;;11626:91;;11688:24;11706:5;11688:24;;;11677:35;;11671:46;;;;11724:85;;11797:5;11790:13;11783:21;11772:32;;11766:43;;;;11816:121;;11889:42;11882:5;11878:54;11867:65;;11861:76;;;;11944:72;;12006:5;11995:16;;11989:27;;;;12024:145;12105:6;12100:3;12095;12082:30;12161:1;12152:6;12147:3;12143:16;12136:27;12075:94;;;;12178:268;12243:1;12250:101;12264:6;12261:1;12258:13;12250:101;;;12340:1;12335:3;12331:11;12325:18;12321:1;12316:3;12312:11;12305:39;12286:2;12283:1;12279:10;12274:15;;12250:101;;;12366:6;12363:1;12360:13;12357:2;;;12431:1;12422:6;12417:3;12413:16;12406:27;12357:2;12227:219;;;;;12454:97;;12542:2;12538:7;12533:2;12526:5;12522:14;12518:28;12508:38;;12502:49;;;;12559:117;12628:24;12646:5;12628:24;;;12621:5;12618:35;12608:2;;12667:1;12664;12657:12;12608:2;12602:74;;12683:117;12752:24;12770:5;12752:24;;;12745:5;12742:35;12732:2;;12791:1;12788;12781:12;12732:2;12726:74;", "source": "pragma solidity >=0.4.22 <0.9.0;\r\npragma experimental ABIEncoderV2;\r\n\r\ncontract Election {\r\n    struct Candidate {\r\n        uint256 id; // ID của ứng cử viên\r\n        string name; // Tên của ứng cử viên\r\n        uint256 voteCount; // Số phiếu bầu\r\n    }\r\n\r\n    // Lưu trạng thái đã bỏ phiếu của cử tri\r\n    mapping(address => bool) public voters;\r\n\r\n    // Đọc/Ghi danh sách ứng cử viên\r\n    mapping(uint256 => Candidate) public candidates;\r\n\r\n    // Số lượng ứng cử viên\r\n    uint256 public candidateCount;\r\n\r\n    // Địa chỉ của chủ sở hữu hợp đồng\r\n    address public owner;\r\n\r\n    // Sự kiện thông báo khi có người bỏ phiếu\r\n    event SuKienBoPhieu(uint256 indexed candidateId);\r\n\r\n    // Sự kiện thông báo khi thêm ứng cử viên mới\r\n    event CandidateAdded(uint256 indexed candidateId, string name);\r\n\r\n    // Modifier chỉ cho phép chủ sở hữu thực hiện\r\n    modifier onlyOwner() {\r\n        require(msg.sender == owner, \"Chỉ chủ sở hữu mới có quyền thực hiện!\");\r\n        _;\r\n    }\r\n\r\n    // Modifier đảm bảo một địa chỉ chỉ được bỏ phiếu một lần\r\n    modifier onlyOnce() {\r\n        require(!voters[msg.sender], \"Bạn đã bỏ phiếu trước đó!\");\r\n        _;\r\n    }\r\n\r\n    constructor() public {\r\n        owner = msg.sender; // Gán chủ sở hữu hợp đồng\r\n        addCandidate(\"Ứng cử viên 1\");\r\n        addCandidate(\"Ứng cử viên 2\");\r\n    }\r\n\r\n    // Thêm ứng cử viên mới (chỉ chủ sở hữu mới có quyền)\r\n    function addCandidate(string memory _name) public onlyOwner {\r\n        candidateCount++;\r\n        candidates[candidateCount] = Candidate(candidateCount, _name, 0);\r\n        emit CandidateAdded(candidateCount, _name);\r\n    }\r\n\r\n    // Hàm bỏ phiếu\r\n    function vote(uint256 _candidateId) public onlyOnce {\r\n        // Bỏ phiếu cho một ứng cử viên hợp lệ\r\n        require(_candidateId > 0 && _candidateId <= candidateCount, \"ID ứng cử viên không hợp lệ!\");\r\n\r\n        // Đánh dấu địa chỉ đã bỏ phiếu\r\n        voters[msg.sender] = true;\r\n\r\n        // Tăng số phiếu bầu của ứng cử viên\r\n        candidates[_candidateId].voteCount++;\r\n\r\n        // Kích hoạt sự kiện bỏ phiếu\r\n        emit SuKienBoPhieu(_candidateId);\r\n    }\r\n\r\n    // Lấy danh sách tất cả ứng cử viên\r\n    function getAllCandidates() public view returns (Candidate[] memory) {\r\n        Candidate[] memory allCandidates = new Candidate[](candidateCount);\r\n        for (uint256 i = 1; i <= candidateCount; i++) {\r\n            allCandidates[i - 1] = candidates[i];\r\n        }\r\n        return allCandidates;\r\n    }\r\n}", "sourcePath": "C:\\Users\\<USER>\\OneDrive\\Máy tính\\Blockchain\\voting-dapp\\contracts\\Election.sol", "ast": {"absolutePath": "project:/contracts/Election.sol", "exportedSymbols": {"Election": [177]}, "id": 178, "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", ">=", "0.4", ".22", "<", "0.9", ".0"], "nodeType": "PragmaDirective", "src": "0:32:0"}, {"id": 2, "literals": ["experimental", "ABIEncoderV2"], "nodeType": "PragmaDirective", "src": "34:33:0"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "id": 177, "linearizedBaseContracts": [177], "name": "Election", "nodeType": "ContractDefinition", "nodes": [{"canonicalName": "Election.Candidate", "id": 9, "members": [{"constant": false, "id": 4, "name": "id", "nodeType": "VariableDeclaration", "scope": 9, "src": "124:10:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 3, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "124:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 6, "name": "name", "nodeType": "VariableDeclaration", "scope": 9, "src": "174:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}, "typeName": {"id": 5, "name": "string", "nodeType": "ElementaryTypeName", "src": "174:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 8, "name": "voteCount", "nodeType": "VariableDeclaration", "scope": 9, "src": "227:17:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 7, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "227:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "name": "Candidate", "nodeType": "StructDefinition", "scope": 177, "src": "96:178:0", "visibility": "public"}, {"constant": false, "id": 13, "name": "voters", "nodeType": "VariableDeclaration", "scope": 177, "src": "342:38:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "typeName": {"id": 12, "keyType": {"id": 10, "name": "address", "nodeType": "ElementaryTypeName", "src": "350:7:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "342:24:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueType": {"id": 11, "name": "bool", "nodeType": "ElementaryTypeName", "src": "361:4:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "value": null, "visibility": "public"}, {"constant": false, "id": 17, "name": "candidates", "nodeType": "VariableDeclaration", "scope": 177, "src": "436:47:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Candidate_$9_storage_$", "typeString": "mapping(uint256 => struct Election.Candidate)"}, "typeName": {"id": 16, "keyType": {"id": 14, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "444:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "436:29:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Candidate_$9_storage_$", "typeString": "mapping(uint256 => struct Election.Candidate)"}, "valueType": {"contractScope": null, "id": 15, "name": "Candidate", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 9, "src": "455:9:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage_ptr", "typeString": "struct Election.Candidate"}}}, "value": null, "visibility": "public"}, {"constant": false, "id": 19, "name": "candidateCount", "nodeType": "VariableDeclaration", "scope": 177, "src": "531:29:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 18, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "531:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "public"}, {"constant": false, "id": 21, "name": "owner", "nodeType": "VariableDeclaration", "scope": 177, "src": "627:20:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 20, "name": "address", "nodeType": "ElementaryTypeName", "src": "627:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "public"}, {"anonymous": false, "documentation": null, "id": 25, "name": "Su<PERSON><PERSON><PERSON><PERSON>", "nodeType": "EventDefinition", "parameters": {"id": 24, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 23, "indexed": true, "name": "candidateId", "nodeType": "VariableDeclaration", "scope": 25, "src": "738:27:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 22, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "738:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "737:29:0"}, "src": "718:49:0"}, {"anonymous": false, "documentation": null, "id": 31, "name": "CandidateAdded", "nodeType": "EventDefinition", "parameters": {"id": 30, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 27, "indexed": true, "name": "candidateId", "nodeType": "VariableDeclaration", "scope": 31, "src": "861:27:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 26, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "861:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}, {"constant": false, "id": 29, "indexed": false, "name": "name", "nodeType": "VariableDeclaration", "scope": 31, "src": "890:11:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 28, "name": "string", "nodeType": "ElementaryTypeName", "src": "890:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": null, "visibility": "internal"}], "src": "860:42:0"}, "src": "840:63:0"}, {"body": {"id": 42, "nodeType": "Block", "src": "996:118:0", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 37, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 34, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 249, "src": "1015:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 35, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1015:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"argumentTypes": null, "id": 36, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "1029:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1015:19:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "4368e1bb89206368e1bba72073e1bb9f2068e1bbaf75206de1bb9b692063c3b320717579e1bb816e207468e1bbb163206869e1bb876e21", "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1036:57:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_423891ab79a86e07782f9a21cb738aa60704d8910bf10a6fc32736beb53c0f25", "typeString": "literal_string \"Chỉ chủ sở hữu mới có quyền thực hiện!\""}, "value": "Chỉ chủ sở hữu mới có quyền thực hiện!"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_423891ab79a86e07782f9a21cb738aa60704d8910bf10a6fc32736beb53c0f25", "typeString": "literal_string \"Chỉ chủ sở hữu mới có quyền thực hiện!\""}], "id": 33, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [252, 253], "referencedDeclaration": 253, "src": "1007:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 39, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1007:87:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 40, "nodeType": "ExpressionStatement", "src": "1007:87:0"}, {"id": 41, "nodeType": "PlaceholderStatement", "src": "1105:1:0"}]}, "documentation": null, "id": 43, "name": "only<PERSON><PERSON>er", "nodeType": "ModifierDefinition", "parameters": {"id": 32, "nodeType": "ParameterList", "parameters": [], "src": "993:2:0"}, "src": "975:139:0", "visibility": "internal"}, {"body": {"id": 55, "nodeType": "Block", "src": "1231:101:0", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 50, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "1250:19:0", "subExpression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 46, "name": "voters", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13, "src": "1251:6:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 49, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 47, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 249, "src": "1258:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 48, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1258:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "1251:18:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "42e1baa16e20c491c3a32062e1bb8f20706869e1babf75207472c6b0e1bb9b6320c491c3b321", "id": 51, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1271:40:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_3fd306dac1e6842feaf81409f29e44b611803624a0a6377594d00d6512a3baec", "typeString": "literal_string \"Bạn đã bỏ phiếu trước đó!\""}, "value": "Bạn đã bỏ phiếu trước đó!"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3fd306dac1e6842feaf81409f29e44b611803624a0a6377594d00d6512a3baec", "typeString": "literal_string \"Bạn đã bỏ phiếu trước đó!\""}], "id": 45, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [252, 253], "referencedDeclaration": 253, "src": "1242:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 52, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1242:70:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 53, "nodeType": "ExpressionStatement", "src": "1242:70:0"}, {"id": 54, "nodeType": "PlaceholderStatement", "src": "1323:1:0"}]}, "documentation": null, "id": 56, "name": "onlyOnce", "nodeType": "ModifierDefinition", "parameters": {"id": 44, "nodeType": "ParameterList", "parameters": [], "src": "1228:2:0"}, "src": "1211:121:0", "visibility": "internal"}, {"body": {"id": 72, "nodeType": "Block", "src": "1361:166:0", "statements": [{"expression": {"argumentTypes": null, "id": 62, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 59, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 21, "src": "1372:5:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 60, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 249, "src": "1380:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 61, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "1380:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "src": "1372:18:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 63, "nodeType": "ExpressionStatement", "src": "1372:18:0"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "hexValue": "e1bba86e672063e1bbad207669c3aa6e2031", "id": 65, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1453:20:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_f1e640e5e996e21716283f93a522311426b4b4f0dbc759a2e1da212a45bfeead", "typeString": "literal_string \"Ứng cử viên 1\""}, "value": "Ứng cử viên 1"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f1e640e5e996e21716283f93a522311426b4b4f0dbc759a2e1da212a45bfeead", "typeString": "literal_string \"Ứng cử viên 1\""}], "id": 64, "name": "addCandidate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 99, "src": "1440:12:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 66, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1440:34:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 67, "nodeType": "ExpressionStatement", "src": "1440:34:0"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "hexValue": "e1bba86e672063e1bbad207669c3aa6e2032", "id": 69, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "1498:20:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_f1c0603aaf94ed102a27c6dc2424eb2cd5c0adf422b8e6ada49bf0b993d60401", "typeString": "literal_string \"Ứng cử viên 2\""}, "value": "Ứng cử viên 2"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f1c0603aaf94ed102a27c6dc2424eb2cd5c0adf422b8e6ada49bf0b993d60401", "typeString": "literal_string \"Ứng cử viên 2\""}], "id": 68, "name": "addCandidate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 99, "src": "1485:12:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_string_memory_ptr_$returns$__$", "typeString": "function (string memory)"}}, "id": 70, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1485:34:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 71, "nodeType": "ExpressionStatement", "src": "1485:34:0"}]}, "documentation": null, "id": 73, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nodeType": "FunctionDefinition", "parameters": {"id": 57, "nodeType": "ParameterList", "parameters": [], "src": "1351:2:0"}, "returnParameters": {"id": 58, "nodeType": "ParameterList", "parameters": [], "src": "1361:0:0"}, "scope": 177, "src": "1340:187:0", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 98, "nodeType": "Block", "src": "1675:163:0", "statements": [{"expression": {"argumentTypes": null, "id": 81, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "1686:16:0", "subExpression": {"argumentTypes": null, "id": 80, "name": "candidateCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "1686:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 82, "nodeType": "ExpressionStatement", "src": "1686:16:0"}, {"expression": {"argumentTypes": null, "id": 91, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 83, "name": "candidates", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17, "src": "1713:10:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Candidate_$9_storage_$", "typeString": "mapping(uint256 => struct Election.Candidate storage ref)"}}, "id": 85, "indexExpression": {"argumentTypes": null, "id": 84, "name": "candidateCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "1724:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "1713:26:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage", "typeString": "struct Election.Candidate storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 87, "name": "candidateCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "1752:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"argumentTypes": null, "id": 88, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 75, "src": "1768:5:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}, {"argumentTypes": null, "hexValue": "30", "id": 89, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1775:1:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 86, "name": "Candidate", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 9, "src": "1742:9:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Candidate_$9_storage_ptr_$", "typeString": "type(struct Election.Candidate storage pointer)"}}, "id": 90, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1742:35:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_memory", "typeString": "struct Election.Candidate memory"}}, "src": "1713:64:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage", "typeString": "struct Election.Candidate storage ref"}}, "id": 92, "nodeType": "ExpressionStatement", "src": "1713:64:0"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 94, "name": "candidateCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "1808:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"argumentTypes": null, "id": 95, "name": "_name", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 75, "src": "1824:5:0", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "id": 93, "name": "CandidateAdded", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 31, "src": "1793:14:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_string_memory_ptr_$returns$__$", "typeString": "function (uint256,string memory)"}}, "id": 96, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1793:37:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 97, "nodeType": "EmitStatement", "src": "1788:42:0"}]}, "documentation": null, "id": 99, "implemented": true, "kind": "function", "modifiers": [{"arguments": null, "id": 78, "modifierName": {"argumentTypes": null, "id": 77, "name": "only<PERSON><PERSON>er", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 43, "src": "1665:9:0", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "1665:9:0"}], "name": "addCandidate", "nodeType": "FunctionDefinition", "parameters": {"id": 76, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 75, "name": "_name", "nodeType": "VariableDeclaration", "scope": 99, "src": "1637:19:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_string_memory_ptr", "typeString": "string"}, "typeName": {"id": 74, "name": "string", "nodeType": "ElementaryTypeName", "src": "1637:6:0", "typeDescriptions": {"typeIdentifier": "t_string_storage_ptr", "typeString": "string"}}, "value": null, "visibility": "internal"}], "src": "1636:21:0"}, "returnParameters": {"id": 79, "nodeType": "ParameterList", "parameters": [], "src": "1675:0:0"}, "scope": 177, "src": "1615:223:0", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 134, "nodeType": "Block", "src": "1924:481:0", "statements": [{"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 113, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 109, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 107, "name": "_candidateId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2006:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"argumentTypes": null, "hexValue": "30", "id": 108, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2021:1:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "2006:16:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 112, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 110, "name": "_candidateId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2026:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "id": 111, "name": "candidateCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "2042:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2026:30:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "2006:50:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"argumentTypes": null, "hexValue": "494420e1bba96e672063e1bbad207669c3aa6e206b68c3b46e672068e1bba370206ce1bb8721", "id": 114, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2058:40:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_stringliteral_7b1903ce49e56410de246788658f55fad81fc9ca1d0d13f956f51a927f956d59", "typeString": "literal_string \"ID ứng cử viên không hợp lệ!\""}, "value": "ID ứng cử viên không hợp lệ!"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_7b1903ce49e56410de246788658f55fad81fc9ca1d0d13f956f51a927f956d59", "typeString": "literal_string \"ID ứng cử viên không hợp lệ!\""}], "id": 106, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [252, 253], "referencedDeclaration": 253, "src": "1998:7:0", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 115, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "1998:101:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 116, "nodeType": "ExpressionStatement", "src": "1998:101:0"}, {"expression": {"argumentTypes": null, "id": 122, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 117, "name": "voters", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13, "src": "2168:6:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 120, "indexExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 118, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 249, "src": "2175:3:0", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 119, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "2175:10:0", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2168:18:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "hexValue": "74727565", "id": 121, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2189:4:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "2168:25:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 123, "nodeType": "ExpressionStatement", "src": "2168:25:0"}, {"expression": {"argumentTypes": null, "id": 128, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "2266:36:0", "subExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 124, "name": "candidates", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17, "src": "2266:10:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Candidate_$9_storage_$", "typeString": "mapping(uint256 => struct Election.Candidate storage ref)"}}, "id": 126, "indexExpression": {"argumentTypes": null, "id": 125, "name": "_candidateId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2277:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2266:24:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage", "typeString": "struct Election.Candidate storage ref"}}, "id": 127, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberName": "voteCount", "nodeType": "MemberAccess", "referencedDeclaration": 8, "src": "2266:34:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 129, "nodeType": "ExpressionStatement", "src": "2266:36:0"}, {"eventCall": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 131, "name": "_candidateId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2384:12:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 130, "name": "Su<PERSON><PERSON><PERSON><PERSON>", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 25, "src": "2370:13:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 132, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2370:27:0", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 133, "nodeType": "EmitStatement", "src": "2365:32:0"}]}, "documentation": null, "id": 135, "implemented": true, "kind": "function", "modifiers": [{"arguments": null, "id": 104, "modifierName": {"argumentTypes": null, "id": 103, "name": "onlyOnce", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 56, "src": "1915:8:0", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "1915:8:0"}], "name": "vote", "nodeType": "FunctionDefinition", "parameters": {"id": 102, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 101, "name": "_candidateId", "nodeType": "VariableDeclaration", "scope": 135, "src": "1886:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 100, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1886:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "1885:22:0"}, "returnParameters": {"id": 105, "nodeType": "ParameterList", "parameters": [], "src": "1924:0:0"}, "scope": 177, "src": "1872:533:0", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 175, "nodeType": "Block", "src": "2535:235:0", "statements": [{"assignments": [144], "declarations": [{"constant": false, "id": 144, "name": "allCandidates", "nodeType": "VariableDeclaration", "scope": 175, "src": "2546:32:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_memory_$dyn_memory_ptr", "typeString": "struct Election.Candidate[]"}, "typeName": {"baseType": {"contractScope": null, "id": 142, "name": "Candidate", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 9, "src": "2546:9:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage_ptr", "typeString": "struct Election.Candidate"}}, "id": 143, "length": null, "nodeType": "ArrayTypeName", "src": "2546:11:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_storage_$dyn_storage_ptr", "typeString": "struct Election.Candidate[]"}}, "value": null, "visibility": "internal"}], "id": 150, "initialValue": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 148, "name": "candidateCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "2597:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 147, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "NewExpression", "src": "2581:15:0", "typeDescriptions": {"typeIdentifier": "t_function_objectcreation_pure$_t_uint256_$returns$_t_array$_t_struct$_Candidate_$9_memory_$dyn_memory_$", "typeString": "function (uint256) pure returns (struct Election.Candidate memory[] memory)"}, "typeName": {"baseType": {"contractScope": null, "id": 145, "name": "Candidate", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 9, "src": "2585:9:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage_ptr", "typeString": "struct Election.Candidate"}}, "id": 146, "length": null, "nodeType": "ArrayTypeName", "src": "2585:11:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_storage_$dyn_storage_ptr", "typeString": "struct Election.Candidate[]"}}}, "id": 149, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "2581:31:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_memory_$dyn_memory", "typeString": "struct Election.Candidate memory[] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "2546:66:0"}, {"body": {"id": 171, "nodeType": "Block", "src": "2669:63:0", "statements": [{"expression": {"argumentTypes": null, "id": 169, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 161, "name": "allCandidates", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 144, "src": "2684:13:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_memory_$dyn_memory_ptr", "typeString": "struct Election.Candidate memory[] memory"}}, "id": 165, "indexExpression": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 164, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 162, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "2698:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "-", "rightExpression": {"argumentTypes": null, "hexValue": "31", "id": 163, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2702:1:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "2698:5:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2684:20:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_memory", "typeString": "struct Election.Candidate memory"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "baseExpression": {"argumentTypes": null, "id": 166, "name": "candidates", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 17, "src": "2707:10:0", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Candidate_$9_storage_$", "typeString": "mapping(uint256 => struct Election.Candidate storage ref)"}}, "id": 168, "indexExpression": {"argumentTypes": null, "id": 167, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "2718:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "2707:13:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage", "typeString": "struct Election.Candidate storage ref"}}, "src": "2684:36:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_memory", "typeString": "struct Election.Candidate memory"}}, "id": 170, "nodeType": "ExpressionStatement", "src": "2684:36:0"}]}, "condition": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 157, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "id": 155, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "2643:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"argumentTypes": null, "id": 156, "name": "candidateCount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 19, "src": "2648:14:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2643:19:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 172, "initializationExpression": {"assignments": [152], "declarations": [{"constant": false, "id": 152, "name": "i", "nodeType": "VariableDeclaration", "scope": 172, "src": "2628:9:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 151, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2628:7:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "id": 154, "initialValue": {"argumentTypes": null, "hexValue": "31", "id": 153, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2640:1:0", "subdenomination": null, "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "nodeType": "VariableDeclarationStatement", "src": "2628:13:0"}, "loopExpression": {"expression": {"argumentTypes": null, "id": 159, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "2664:3:0", "subExpression": {"argumentTypes": null, "id": 158, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 152, "src": "2664:1:0", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 160, "nodeType": "ExpressionStatement", "src": "2664:3:0"}, "nodeType": "ForStatement", "src": "2623:109:0"}, {"expression": {"argumentTypes": null, "id": 173, "name": "allCandidates", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 144, "src": "2749:13:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_memory_$dyn_memory_ptr", "typeString": "struct Election.Candidate memory[] memory"}}, "functionReturnParameters": 140, "id": 174, "nodeType": "Return", "src": "2742:20:0"}]}, "documentation": null, "id": 176, "implemented": true, "kind": "function", "modifiers": [], "name": "getAllCandidates", "nodeType": "FunctionDefinition", "parameters": {"id": 136, "nodeType": "ParameterList", "parameters": [], "src": "2491:2:0"}, "returnParameters": {"id": 140, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 139, "name": "", "nodeType": "VariableDeclaration", "scope": 176, "src": "2515:18:0", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_memory_$dyn_memory_ptr", "typeString": "struct Election.Candidate[]"}, "typeName": {"baseType": {"contractScope": null, "id": 137, "name": "Candidate", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 9, "src": "2515:9:0", "typeDescriptions": {"typeIdentifier": "t_struct$_Candidate_$9_storage_ptr", "typeString": "struct Election.Candidate"}}, "id": 138, "length": null, "nodeType": "ArrayTypeName", "src": "2515:11:0", "typeDescriptions": {"typeIdentifier": "t_array$_t_struct$_Candidate_$9_storage_$dyn_storage_ptr", "typeString": "struct Election.Candidate[]"}}, "value": null, "visibility": "internal"}], "src": "2514:20:0"}, "scope": 177, "src": "2466:304:0", "stateMutability": "view", "superFunction": null, "visibility": "public"}], "scope": 178, "src": "71:2702:0"}], "src": "0:2773:0"}, "legacyAST": {"attributes": {"absolutePath": "project:/contracts/Election.sol", "exportedSymbols": {"Election": [177]}}, "children": [{"attributes": {"literals": ["solidity", ">=", "0.4", ".22", "<", "0.9", ".0"]}, "id": 1, "name": "PragmaDirective", "src": "0:32:0"}, {"attributes": {"literals": ["experimental", "ABIEncoderV2"]}, "id": 2, "name": "PragmaDirective", "src": "34:33:0"}, {"attributes": {"baseContracts": [null], "contractDependencies": [null], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "linearizedBaseContracts": [177], "name": "Election", "scope": 178}, "children": [{"attributes": {"canonicalName": "Election.Candidate", "name": "Candidate", "scope": 177, "visibility": "public"}, "children": [{"attributes": {"constant": false, "name": "id", "scope": 9, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 3, "name": "ElementaryTypeName", "src": "124:7:0"}], "id": 4, "name": "VariableDeclaration", "src": "124:10:0"}, {"attributes": {"constant": false, "name": "name", "scope": 9, "stateVariable": false, "storageLocation": "default", "type": "string", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "string", "type": "string"}, "id": 5, "name": "ElementaryTypeName", "src": "174:6:0"}], "id": 6, "name": "VariableDeclaration", "src": "174:11:0"}, {"attributes": {"constant": false, "name": "voteCount", "scope": 9, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 7, "name": "ElementaryTypeName", "src": "227:7:0"}], "id": 8, "name": "VariableDeclaration", "src": "227:17:0"}], "id": 9, "name": "StructDefinition", "src": "96:178:0"}, {"attributes": {"constant": false, "name": "voters", "scope": 177, "stateVariable": true, "storageLocation": "default", "type": "mapping(address => bool)", "value": null, "visibility": "public"}, "children": [{"attributes": {"type": "mapping(address => bool)"}, "children": [{"attributes": {"name": "address", "type": "address"}, "id": 10, "name": "ElementaryTypeName", "src": "350:7:0"}, {"attributes": {"name": "bool", "type": "bool"}, "id": 11, "name": "ElementaryTypeName", "src": "361:4:0"}], "id": 12, "name": "Mapping", "src": "342:24:0"}], "id": 13, "name": "VariableDeclaration", "src": "342:38:0"}, {"attributes": {"constant": false, "name": "candidates", "scope": 177, "stateVariable": true, "storageLocation": "default", "type": "mapping(uint256 => struct Election.Candidate)", "value": null, "visibility": "public"}, "children": [{"attributes": {"type": "mapping(uint256 => struct Election.Candidate)"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 14, "name": "ElementaryTypeName", "src": "444:7:0"}, {"attributes": {"contractScope": null, "name": "Candidate", "referencedDeclaration": 9, "type": "struct Election.Candidate"}, "id": 15, "name": "UserDefinedTypeName", "src": "455:9:0"}], "id": 16, "name": "Mapping", "src": "436:29:0"}], "id": 17, "name": "VariableDeclaration", "src": "436:47:0"}, {"attributes": {"constant": false, "name": "candidateCount", "scope": 177, "stateVariable": true, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "public"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 18, "name": "ElementaryTypeName", "src": "531:7:0"}], "id": 19, "name": "VariableDeclaration", "src": "531:29:0"}, {"attributes": {"constant": false, "name": "owner", "scope": 177, "stateVariable": true, "storageLocation": "default", "type": "address", "value": null, "visibility": "public"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 20, "name": "ElementaryTypeName", "src": "627:7:0"}], "id": 21, "name": "VariableDeclaration", "src": "627:20:0"}, {"attributes": {"anonymous": false, "documentation": null, "name": "Su<PERSON><PERSON><PERSON><PERSON>"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "name": "candidateId", "scope": 25, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 22, "name": "ElementaryTypeName", "src": "738:7:0"}], "id": 23, "name": "VariableDeclaration", "src": "738:27:0"}], "id": 24, "name": "ParameterList", "src": "737:29:0"}], "id": 25, "name": "EventDefinition", "src": "718:49:0"}, {"attributes": {"anonymous": false, "documentation": null, "name": "CandidateAdded"}, "children": [{"children": [{"attributes": {"constant": false, "indexed": true, "name": "candidateId", "scope": 31, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 26, "name": "ElementaryTypeName", "src": "861:7:0"}], "id": 27, "name": "VariableDeclaration", "src": "861:27:0"}, {"attributes": {"constant": false, "indexed": false, "name": "name", "scope": 31, "stateVariable": false, "storageLocation": "default", "type": "string", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "string", "type": "string"}, "id": 28, "name": "ElementaryTypeName", "src": "890:6:0"}], "id": 29, "name": "VariableDeclaration", "src": "890:11:0"}], "id": 30, "name": "ParameterList", "src": "860:42:0"}], "id": 31, "name": "EventDefinition", "src": "840:63:0"}, {"attributes": {"documentation": null, "name": "only<PERSON><PERSON>er", "visibility": "internal"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 32, "name": "ParameterList", "src": "993:2:0"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_423891ab79a86e07782f9a21cb738aa60704d8910bf10a6fc32736beb53c0f25", "typeString": "literal_string \"Chỉ chủ sở hữu mới có quyền thực hiện!\""}], "overloadedDeclarations": [252, 253], "referencedDeclaration": 253, "type": "function (bool,string memory) pure", "value": "require"}, "id": 33, "name": "Identifier", "src": "1007:7:0"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "==", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 249, "type": "msg", "value": "msg"}, "id": 34, "name": "Identifier", "src": "1015:3:0"}], "id": 35, "name": "MemberAccess", "src": "1015:10:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 21, "type": "address", "value": "owner"}, "id": 36, "name": "Identifier", "src": "1029:5:0"}], "id": 37, "name": "BinaryOperation", "src": "1015:19:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "4368e1bb89206368e1bba72073e1bb9f2068e1bbaf75206de1bb9b692063c3b320717579e1bb816e207468e1bbb163206869e1bb876e21", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Chỉ chủ sở hữu mới có quyền thực hiện!\"", "value": "Chỉ chủ sở hữu mới có quyền thực hiện!"}, "id": 38, "name": "Literal", "src": "1036:57:0"}], "id": 39, "name": "FunctionCall", "src": "1007:87:0"}], "id": 40, "name": "ExpressionStatement", "src": "1007:87:0"}, {"id": 41, "name": "PlaceholderStatement", "src": "1105:1:0"}], "id": 42, "name": "Block", "src": "996:118:0"}], "id": 43, "name": "ModifierDefinition", "src": "975:139:0"}, {"attributes": {"documentation": null, "name": "onlyOnce", "visibility": "internal"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 44, "name": "ParameterList", "src": "1228:2:0"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3fd306dac1e6842feaf81409f29e44b611803624a0a6377594d00d6512a3baec", "typeString": "literal_string \"Bạn đã bỏ phiếu trước đó!\""}], "overloadedDeclarations": [252, 253], "referencedDeclaration": 253, "type": "function (bool,string memory) pure", "value": "require"}, "id": 45, "name": "Identifier", "src": "1242:7:0"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "!", "prefix": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 13, "type": "mapping(address => bool)", "value": "voters"}, "id": 46, "name": "Identifier", "src": "1251:6:0"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 249, "type": "msg", "value": "msg"}, "id": 47, "name": "Identifier", "src": "1258:3:0"}], "id": 48, "name": "MemberAccess", "src": "1258:10:0"}], "id": 49, "name": "IndexAccess", "src": "1251:18:0"}], "id": 50, "name": "UnaryOperation", "src": "1250:19:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "42e1baa16e20c491c3a32062e1bb8f20706869e1babf75207472c6b0e1bb9b6320c491c3b321", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Bạn đã bỏ phiếu trước đó!\"", "value": "Bạn đã bỏ phiếu trước đó!"}, "id": 51, "name": "Literal", "src": "1271:40:0"}], "id": 52, "name": "FunctionCall", "src": "1242:70:0"}], "id": 53, "name": "ExpressionStatement", "src": "1242:70:0"}, {"id": 54, "name": "PlaceholderStatement", "src": "1323:1:0"}], "id": 55, "name": "Block", "src": "1231:101:0"}], "id": 56, "name": "ModifierDefinition", "src": "1211:121:0"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": true, "kind": "constructor", "modifiers": [null], "name": "", "scope": 177, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 57, "name": "ParameterList", "src": "1351:2:0"}, {"attributes": {"parameters": [null]}, "children": [], "id": 58, "name": "ParameterList", "src": "1361:0:0"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "address"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 21, "type": "address", "value": "owner"}, "id": 59, "name": "Identifier", "src": "1372:5:0"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 249, "type": "msg", "value": "msg"}, "id": 60, "name": "Identifier", "src": "1380:3:0"}], "id": 61, "name": "MemberAccess", "src": "1380:10:0"}], "id": 62, "name": "Assignment", "src": "1372:18:0"}], "id": 63, "name": "ExpressionStatement", "src": "1372:18:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f1e640e5e996e21716283f93a522311426b4b4f0dbc759a2e1da212a45bfeead", "typeString": "literal_string \"Ứng cử viên 1\""}], "overloadedDeclarations": [null], "referencedDeclaration": 99, "type": "function (string memory)", "value": "addCandidate"}, "id": 64, "name": "Identifier", "src": "1440:12:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "e1bba86e672063e1bbad207669c3aa6e2031", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Ứng cử viên 1\"", "value": "Ứng cử viên 1"}, "id": 65, "name": "Literal", "src": "1453:20:0"}], "id": 66, "name": "FunctionCall", "src": "1440:34:0"}], "id": 67, "name": "ExpressionStatement", "src": "1440:34:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_f1c0603aaf94ed102a27c6dc2424eb2cd5c0adf422b8e6ada49bf0b993d60401", "typeString": "literal_string \"Ứng cử viên 2\""}], "overloadedDeclarations": [null], "referencedDeclaration": 99, "type": "function (string memory)", "value": "addCandidate"}, "id": 68, "name": "Identifier", "src": "1485:12:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "e1bba86e672063e1bbad207669c3aa6e2032", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"Ứng cử viên 2\"", "value": "Ứng cử viên 2"}, "id": 69, "name": "Literal", "src": "1498:20:0"}], "id": 70, "name": "FunctionCall", "src": "1485:34:0"}], "id": 71, "name": "ExpressionStatement", "src": "1485:34:0"}], "id": 72, "name": "Block", "src": "1361:166:0"}], "id": 73, "name": "FunctionDefinition", "src": "1340:187:0"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "name": "addCandidate", "scope": 177, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_name", "scope": 99, "stateVariable": false, "storageLocation": "memory", "type": "string", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "string", "type": "string"}, "id": 74, "name": "ElementaryTypeName", "src": "1637:6:0"}], "id": 75, "name": "VariableDeclaration", "src": "1637:19:0"}], "id": 76, "name": "ParameterList", "src": "1636:21:0"}, {"attributes": {"parameters": [null]}, "children": [], "id": 79, "name": "ParameterList", "src": "1675:0:0"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 43, "type": "modifier ()", "value": "only<PERSON><PERSON>er"}, "id": 77, "name": "Identifier", "src": "1665:9:0"}], "id": 78, "name": "ModifierInvocation", "src": "1665:9:0"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "++", "prefix": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 19, "type": "uint256", "value": "candidateCount"}, "id": 80, "name": "Identifier", "src": "1686:14:0"}], "id": 81, "name": "UnaryOperation", "src": "1686:16:0"}], "id": 82, "name": "ExpressionStatement", "src": "1686:16:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "struct Election.Candidate storage ref"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "struct Election.Candidate storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 17, "type": "mapping(uint256 => struct Election.Candidate storage ref)", "value": "candidates"}, "id": 83, "name": "Identifier", "src": "1713:10:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 19, "type": "uint256", "value": "candidateCount"}, "id": 84, "name": "Identifier", "src": "1724:14:0"}], "id": 85, "name": "IndexAccess", "src": "1713:26:0"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": true, "lValueRequested": false, "names": [null], "type": "struct Election.Candidate memory", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "overloadedDeclarations": [null], "referencedDeclaration": 9, "type": "type(struct Election.Candidate storage pointer)", "value": "Candidate"}, "id": 86, "name": "Identifier", "src": "1742:9:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 19, "type": "uint256", "value": "candidateCount"}, "id": 87, "name": "Identifier", "src": "1752:14:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 75, "type": "string memory", "value": "_name"}, "id": 88, "name": "Identifier", "src": "1768:5:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 89, "name": "Literal", "src": "1775:1:0"}], "id": 90, "name": "FunctionCall", "src": "1742:35:0"}], "id": 91, "name": "Assignment", "src": "1713:64:0"}], "id": 92, "name": "ExpressionStatement", "src": "1713:64:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_string_memory_ptr", "typeString": "string memory"}], "overloadedDeclarations": [null], "referencedDeclaration": 31, "type": "function (uint256,string memory)", "value": "CandidateAdded"}, "id": 93, "name": "Identifier", "src": "1793:14:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 19, "type": "uint256", "value": "candidateCount"}, "id": 94, "name": "Identifier", "src": "1808:14:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 75, "type": "string memory", "value": "_name"}, "id": 95, "name": "Identifier", "src": "1824:5:0"}], "id": 96, "name": "FunctionCall", "src": "1793:37:0"}], "id": 97, "name": "EmitStatement", "src": "1788:42:0"}], "id": 98, "name": "Block", "src": "1675:163:0"}], "id": 99, "name": "FunctionDefinition", "src": "1615:223:0"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "name": "vote", "scope": 177, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "_candidateId", "scope": 135, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 100, "name": "ElementaryTypeName", "src": "1886:7:0"}], "id": 101, "name": "VariableDeclaration", "src": "1886:20:0"}], "id": 102, "name": "ParameterList", "src": "1885:22:0"}, {"attributes": {"parameters": [null]}, "children": [], "id": 105, "name": "ParameterList", "src": "1924:0:0"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 56, "type": "modifier ()", "value": "onlyOnce"}, "id": 103, "name": "Identifier", "src": "1915:8:0"}], "id": 104, "name": "ModifierInvocation", "src": "1915:8:0"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_7b1903ce49e56410de246788658f55fad81fc9ca1d0d13f956f51a927f956d59", "typeString": "literal_string \"ID ứng cử viên không hợp lệ!\""}], "overloadedDeclarations": [252, 253], "referencedDeclaration": 253, "type": "function (bool,string memory) pure", "value": "require"}, "id": 106, "name": "Identifier", "src": "1998:7:0"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "&&", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": ">", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 101, "type": "uint256", "value": "_candidateId"}, "id": 107, "name": "Identifier", "src": "2006:12:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "30", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 0", "value": "0"}, "id": 108, "name": "Literal", "src": "2021:1:0"}], "id": 109, "name": "BinaryOperation", "src": "2006:16:0"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 101, "type": "uint256", "value": "_candidateId"}, "id": 110, "name": "Identifier", "src": "2026:12:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 19, "type": "uint256", "value": "candidateCount"}, "id": 111, "name": "Identifier", "src": "2042:14:0"}], "id": 112, "name": "BinaryOperation", "src": "2026:30:0"}], "id": 113, "name": "BinaryOperation", "src": "2006:50:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "494420e1bba96e672063e1bbad207669c3aa6e206b68c3b46e672068e1bba370206ce1bb8721", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "string", "type": "literal_string \"ID ứng cử viên không hợp lệ!\"", "value": "ID ứng cử viên không hợp lệ!"}, "id": 114, "name": "Literal", "src": "2058:40:0"}], "id": 115, "name": "FunctionCall", "src": "1998:101:0"}], "id": 116, "name": "ExpressionStatement", "src": "1998:101:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 13, "type": "mapping(address => bool)", "value": "voters"}, "id": 117, "name": "Identifier", "src": "2168:6:0"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 249, "type": "msg", "value": "msg"}, "id": 118, "name": "Identifier", "src": "2175:3:0"}], "id": 119, "name": "MemberAccess", "src": "2175:10:0"}], "id": 120, "name": "IndexAccess", "src": "2168:18:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "74727565", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "bool", "type": "bool", "value": "true"}, "id": 121, "name": "Literal", "src": "2189:4:0"}], "id": 122, "name": "Assignment", "src": "2168:25:0"}], "id": 123, "name": "ExpressionStatement", "src": "2168:25:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "++", "prefix": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "member_name": "voteCount", "referencedDeclaration": 8, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct Election.Candidate storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 17, "type": "mapping(uint256 => struct Election.Candidate storage ref)", "value": "candidates"}, "id": 124, "name": "Identifier", "src": "2266:10:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 101, "type": "uint256", "value": "_candidateId"}, "id": 125, "name": "Identifier", "src": "2277:12:0"}], "id": 126, "name": "IndexAccess", "src": "2266:24:0"}], "id": 127, "name": "MemberAccess", "src": "2266:34:0"}], "id": 128, "name": "UnaryOperation", "src": "2266:36:0"}], "id": 129, "name": "ExpressionStatement", "src": "2266:36:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "overloadedDeclarations": [null], "referencedDeclaration": 25, "type": "function (uint256)", "value": "Su<PERSON><PERSON><PERSON><PERSON>"}, "id": 130, "name": "Identifier", "src": "2370:13:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 101, "type": "uint256", "value": "_candidateId"}, "id": 131, "name": "Identifier", "src": "2384:12:0"}], "id": 132, "name": "FunctionCall", "src": "2370:27:0"}], "id": 133, "name": "EmitStatement", "src": "2365:32:0"}], "id": 134, "name": "Block", "src": "1924:481:0"}], "id": 135, "name": "FunctionDefinition", "src": "1872:533:0"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "modifiers": [null], "name": "getAllCandidates", "scope": 177, "stateMutability": "view", "superFunction": null, "visibility": "public"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 136, "name": "ParameterList", "src": "2491:2:0"}, {"children": [{"attributes": {"constant": false, "name": "", "scope": 176, "stateVariable": false, "storageLocation": "memory", "type": "struct Election.Candidate[]", "value": null, "visibility": "internal"}, "children": [{"attributes": {"length": null, "type": "struct Election.Candidate[]"}, "children": [{"attributes": {"contractScope": null, "name": "Candidate", "referencedDeclaration": 9, "type": "struct Election.Candidate"}, "id": 137, "name": "UserDefinedTypeName", "src": "2515:9:0"}], "id": 138, "name": "ArrayTypeName", "src": "2515:11:0"}], "id": 139, "name": "VariableDeclaration", "src": "2515:18:0"}], "id": 140, "name": "ParameterList", "src": "2514:20:0"}, {"children": [{"attributes": {"assignments": [144]}, "children": [{"attributes": {"constant": false, "name": "allCandidates", "scope": 175, "stateVariable": false, "storageLocation": "memory", "type": "struct Election.Candidate[]", "value": null, "visibility": "internal"}, "children": [{"attributes": {"length": null, "type": "struct Election.Candidate[]"}, "children": [{"attributes": {"contractScope": null, "name": "Candidate", "referencedDeclaration": 9, "type": "struct Election.Candidate"}, "id": 142, "name": "UserDefinedTypeName", "src": "2546:9:0"}], "id": 143, "name": "ArrayTypeName", "src": "2546:11:0"}], "id": 144, "name": "VariableDeclaration", "src": "2546:32:0"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "struct Election.Candidate memory[] memory", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "type": "function (uint256) pure returns (struct Election.Candidate memory[] memory)"}, "children": [{"attributes": {"length": null, "type": "struct Election.Candidate[]"}, "children": [{"attributes": {"contractScope": null, "name": "Candidate", "referencedDeclaration": 9, "type": "struct Election.Candidate"}, "id": 145, "name": "UserDefinedTypeName", "src": "2585:9:0"}], "id": 146, "name": "ArrayTypeName", "src": "2585:11:0"}], "id": 147, "name": "NewExpression", "src": "2581:15:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 19, "type": "uint256", "value": "candidateCount"}, "id": 148, "name": "Identifier", "src": "2597:14:0"}], "id": 149, "name": "FunctionCall", "src": "2581:31:0"}], "id": 150, "name": "VariableDeclarationStatement", "src": "2546:66:0"}, {"children": [{"attributes": {"assignments": [152]}, "children": [{"attributes": {"constant": false, "name": "i", "scope": 172, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 151, "name": "ElementaryTypeName", "src": "2628:7:0"}], "id": 152, "name": "VariableDeclaration", "src": "2628:9:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "31", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 1", "value": "1"}, "id": 153, "name": "Literal", "src": "2640:1:0"}], "id": 154, "name": "VariableDeclarationStatement", "src": "2628:13:0"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "<=", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "uint256", "value": "i"}, "id": 155, "name": "Identifier", "src": "2643:1:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 19, "type": "uint256", "value": "candidateCount"}, "id": 156, "name": "Identifier", "src": "2648:14:0"}], "id": 157, "name": "BinaryOperation", "src": "2643:19:0"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "++", "prefix": false, "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "uint256", "value": "i"}, "id": 158, "name": "Identifier", "src": "2664:1:0"}], "id": 159, "name": "UnaryOperation", "src": "2664:3:0"}], "id": 160, "name": "ExpressionStatement", "src": "2664:3:0"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "struct Election.Candidate memory"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "type": "struct Election.Candidate memory"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 144, "type": "struct Election.Candidate memory[] memory", "value": "allCandidates"}, "id": 161, "name": "Identifier", "src": "2684:13:0"}, {"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "-", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "uint256", "value": "i"}, "id": 162, "name": "Identifier", "src": "2698:1:0"}, {"attributes": {"argumentTypes": null, "hexvalue": "31", "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "subdenomination": null, "token": "number", "type": "int_const 1", "value": "1"}, "id": 163, "name": "Literal", "src": "2702:1:0"}], "id": 164, "name": "BinaryOperation", "src": "2698:5:0"}], "id": 165, "name": "IndexAccess", "src": "2684:20:0"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "type": "struct Election.Candidate storage ref"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 17, "type": "mapping(uint256 => struct Election.Candidate storage ref)", "value": "candidates"}, "id": 166, "name": "Identifier", "src": "2707:10:0"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 152, "type": "uint256", "value": "i"}, "id": 167, "name": "Identifier", "src": "2718:1:0"}], "id": 168, "name": "IndexAccess", "src": "2707:13:0"}], "id": 169, "name": "Assignment", "src": "2684:36:0"}], "id": 170, "name": "ExpressionStatement", "src": "2684:36:0"}], "id": 171, "name": "Block", "src": "2669:63:0"}], "id": 172, "name": "ForStatement", "src": "2623:109:0"}, {"attributes": {"functionReturnParameters": 140}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 144, "type": "struct Election.Candidate memory[] memory", "value": "allCandidates"}, "id": 173, "name": "Identifier", "src": "2749:13:0"}], "id": 174, "name": "Return", "src": "2742:20:0"}], "id": 175, "name": "Block", "src": "2535:235:0"}], "id": 176, "name": "FunctionDefinition", "src": "2466:304:0"}], "id": 177, "name": "ContractDefinition", "src": "71:2702:0"}], "id": 178, "name": "SourceUnit", "src": "0:2773:0"}, "compiler": {"name": "solc", "version": "0.5.16+commit.9c3226ce.Emscripten.clang"}, "networks": {"5777": {"events": {"0xe83b2a43e7e82d975c8a0a6d2f045153c869e111136a34d1889ab7b598e396a3": {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "candidateId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}], "name": "CandidateAdded", "type": "event"}, "0x723a071da6c61a839585474c6f0a3a333d1c02337291c4d21053ac1883f8d029": {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "candidateId", "type": "uint256"}], "name": "Su<PERSON><PERSON><PERSON><PERSON>", "type": "event"}}, "links": {}, "address": "******************************************", "transactionHash": "0x804fa5011299c281eb73296e9237053ae816b7ce2551b8ccb3e8c4478fc2c036"}}, "schemaVersion": "3.4.16", "updatedAt": "2025-05-04T09:56:39.669Z", "networkType": "ethereum", "devdoc": {"methods": {}}, "userdoc": {"methods": {}}}