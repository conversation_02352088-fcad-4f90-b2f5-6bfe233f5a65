{"contractName": "Migrations", "abi": [{"inputs": [], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"constant": true, "inputs": [], "name": "last_completed_migration", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "completed", "type": "uint256"}], "name": "setCompleted", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "new_address", "type": "address"}], "name": "upgrade", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}], "metadata": "{\"compiler\":{\"version\":\"0.5.16+commit.9c3226ce\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"constant\":true,\"inputs\":[],\"name\":\"last_completed_migration\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":true,\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"payable\":false,\"stateMutability\":\"view\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"completed\",\"type\":\"uint256\"}],\"name\":\"setCompleted\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"constant\":false,\"inputs\":[{\"internalType\":\"address\",\"name\":\"new_address\",\"type\":\"address\"}],\"name\":\"upgrade\",\"outputs\":[],\"payable\":false,\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"methods\":{}},\"userdoc\":{\"methods\":{}}},\"settings\":{\"compilationTarget\":{\"project:/contracts/Migrations.sol\":\"Migrations\"},\"evmVersion\":\"istanbul\",\"libraries\":{},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"project:/contracts/Migrations.sol\":{\"keccak256\":\"0x43cce821328da5db020369cbb1be95fd67bdcfd3848a2132568fc9a3102f8fd1\",\"urls\":[\"bzz-raw://cfa97ce222002b2b53218123181a3539acd549bd106d9763e7d16d893349ab55\",\"dweb:/ipfs/QmRc78L1Zmq5EEPtPu9bK3cpP6pQTVFanVEcetDvA4Vhkw\"]}},\"version\":1}", "bytecode": "0x608060405234801561001057600080fd5b50336000806101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055506102b7806100606000396000f3fe608060405234801561001057600080fd5b506004361061004c5760003560e01c80630900f01014610051578063445df0ac146100955780638da5cb5b146100b3578063fdacd576146100fd575b600080fd5b6100936004803603602081101561006757600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919050505061012b565b005b61009d6101f7565b6040518082815260200191505060405180910390f35b6100bb6101fd565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b6101296004803603602081101561011357600080fd5b8101908080359060200190929190505050610222565b005b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614156101f45760008190508073ffffffffffffffffffffffffffffffffffffffff1663fdacd5766001546040518263ffffffff1660e01b815260040180828152602001915050600060405180830381600087803b1580156101da57600080fd5b505af11580156101ee573d6000803e3d6000fd5b50505050505b50565b60015481565b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff16141561027f57806001819055505b5056fea265627a7a723158202ced410af8abe7f7a769f7be9dc8e3e4e140034f87d3f1ce3010aa66958537c164736f6c63430005100032", "deployedBytecode": "0x608060405234801561001057600080fd5b506004361061004c5760003560e01c80630900f01014610051578063445df0ac146100955780638da5cb5b146100b3578063fdacd576146100fd575b600080fd5b6100936004803603602081101561006757600080fd5b81019080803573ffffffffffffffffffffffffffffffffffffffff16906020019092919050505061012b565b005b61009d6101f7565b6040518082815260200191505060405180910390f35b6100bb6101fd565b604051808273ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff16815260200191505060405180910390f35b6101296004803603602081101561011357600080fd5b8101908080359060200190929190505050610222565b005b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614156101f45760008190508073ffffffffffffffffffffffffffffffffffffffff1663fdacd5766001546040518263ffffffff1660e01b815260040180828152602001915050600060405180830381600087803b1580156101da57600080fd5b505af11580156101ee573d6000803e3d6000fd5b50505050505b50565b60015481565b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b6000809054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff16141561027f57806001819055505b5056fea265627a7a723158202ced410af8abe7f7a769f7be9dc8e3e4e140034f87d3f1ce3010aa66958537c164736f6c63430005100032", "sourceMap": "36:503:1:-;;;199:52;8:9:-1;5:2;;;30:1;27;20:12;5:2;199:52:1;235:10;227:5;;:18;;;;;;;;;;;;;;;;;;36:503;;;;;;", "deployedSourceMap": "36:503:1:-;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;36:503:1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;368:168;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;368:168:1;;;;;;;;;;;;;;;;;;;:::i;:::-;;86:39;;;:::i;:::-;;;;;;;;;;;;;;;;;;;61:20;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;257:105;;;;;;13:2:-1;8:3;5:11;2:2;;;29:1;26;19:12;2:2;257:105:1;;;;;;;;;;;;;;;;;:::i;:::-;;368:168;179:5;;;;;;;;;;;165:19;;:10;:19;;;161:26;;;431:19;464:11;431:45;;483:8;:21;;;505:24;;483:47;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8:9:-1;5:2;;;30:1;27;20:12;5:2;483:47:1;;;;8:9:-1;5:2;;;45:16;42:1;39;24:38;77:16;74:1;67:27;5:2;483:47:1;;;;186:1;161:26;368:168;:::o;86:39::-;;;;:::o;61:20::-;;;;;;;;;;;;;:::o;257:105::-;179:5;;;;;;;;;;;165:19;;:10;:19;;;161:26;;;347:9;320:24;:36;;;;161:26;257:105;:::o", "source": "pragma solidity >=0.4.22 <0.7.0;\r\n\r\ncontract Migrations {\r\n  address public owner;\r\n  uint256 public last_completed_migration;\r\n\r\n  modifier restricted() {\r\n    if (msg.sender == owner) _;\r\n  }\r\n\r\n  constructor() public {\r\n    owner = msg.sender;\r\n  }\r\n\r\n  function setCompleted(uint completed) public restricted {\r\n    last_completed_migration = completed;\r\n  }\r\n\r\n  function upgrade(address new_address) public restricted {\r\n    Migrations upgraded = Migrations(new_address);\r\n    upgraded.setCompleted(last_completed_migration);\r\n  }\r\n}\r\n", "sourcePath": "C:\\Users\\<USER>\\OneDrive\\Máy tính\\Blockchain\\voting-dapp\\contracts\\Migrations.sol", "ast": {"absolutePath": "project:/contracts/Migrations.sol", "exportedSymbols": {"Migrations": [234]}, "id": 235, "nodeType": "SourceUnit", "nodes": [{"id": 179, "literals": ["solidity", ">=", "0.4", ".22", "<", "0.7", ".0"], "nodeType": "PragmaDirective", "src": "0:32:1"}, {"baseContracts": [], "contractDependencies": [], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "id": 234, "linearizedBaseContracts": [234], "name": "Migrations", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 181, "name": "owner", "nodeType": "VariableDeclaration", "scope": 234, "src": "61:20:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 180, "name": "address", "nodeType": "ElementaryTypeName", "src": "61:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "public"}, {"constant": false, "id": 183, "name": "last_completed_migration", "nodeType": "VariableDeclaration", "scope": 234, "src": "86:39:1", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 182, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "86:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "public"}, {"body": {"id": 191, "nodeType": "Block", "src": "154:39:1", "statements": [{"condition": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 188, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 185, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 249, "src": "165:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 186, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "165:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"argumentTypes": null, "id": 187, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 181, "src": "179:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "165:19:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": null, "id": 190, "nodeType": "IfStatement", "src": "161:26:1", "trueBody": {"id": 189, "nodeType": "PlaceholderStatement", "src": "186:1:1"}}]}, "documentation": null, "id": 192, "name": "restricted", "nodeType": "ModifierDefinition", "parameters": {"id": 184, "nodeType": "ParameterList", "parameters": [], "src": "151:2:1"}, "src": "132:61:1", "visibility": "internal"}, {"body": {"id": 200, "nodeType": "Block", "src": "220:31:1", "statements": [{"expression": {"argumentTypes": null, "id": 198, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 195, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 181, "src": "227:5:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "expression": {"argumentTypes": null, "id": 196, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 249, "src": "235:3:1", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 197, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "sender", "nodeType": "MemberAccess", "referencedDeclaration": null, "src": "235:10:1", "typeDescriptions": {"typeIdentifier": "t_address_payable", "typeString": "address payable"}}, "src": "227:18:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 199, "nodeType": "ExpressionStatement", "src": "227:18:1"}]}, "documentation": null, "id": 201, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nodeType": "FunctionDefinition", "parameters": {"id": 193, "nodeType": "ParameterList", "parameters": [], "src": "210:2:1"}, "returnParameters": {"id": 194, "nodeType": "ParameterList", "parameters": [], "src": "220:0:1"}, "scope": 234, "src": "199:52:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 212, "nodeType": "Block", "src": "313:49:1", "statements": [{"expression": {"argumentTypes": null, "id": 210, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"argumentTypes": null, "id": 208, "name": "last_completed_migration", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 183, "src": "320:24:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"argumentTypes": null, "id": 209, "name": "completed", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 203, "src": "347:9:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "320:36:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 211, "nodeType": "ExpressionStatement", "src": "320:36:1"}]}, "documentation": null, "id": 213, "implemented": true, "kind": "function", "modifiers": [{"arguments": null, "id": 206, "modifierName": {"argumentTypes": null, "id": 205, "name": "restricted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 192, "src": "302:10:1", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "302:10:1"}], "name": "setCompleted", "nodeType": "FunctionDefinition", "parameters": {"id": 204, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 203, "name": "completed", "nodeType": "VariableDeclaration", "scope": 213, "src": "279:14:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 202, "name": "uint", "nodeType": "ElementaryTypeName", "src": "279:4:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": null, "visibility": "internal"}], "src": "278:16:1"}, "returnParameters": {"id": 207, "nodeType": "ParameterList", "parameters": [], "src": "313:0:1"}, "scope": 234, "src": "257:105:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, {"body": {"id": 232, "nodeType": "Block", "src": "424:112:1", "statements": [{"assignments": [221], "declarations": [{"constant": false, "id": 221, "name": "upgraded", "nodeType": "VariableDeclaration", "scope": 232, "src": "431:19:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_Migrations_$234", "typeString": "contract Migrations"}, "typeName": {"contractScope": null, "id": 220, "name": "Migrations", "nodeType": "UserDefinedTypeName", "referencedDeclaration": 234, "src": "431:10:1", "typeDescriptions": {"typeIdentifier": "t_contract$_Migrations_$234", "typeString": "contract Migrations"}}, "value": null, "visibility": "internal"}], "id": 225, "initialValue": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 223, "name": "new_address", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 215, "src": "464:11:1", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 222, "name": "Migrations", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 234, "src": "453:10:1", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_Migrations_$234_$", "typeString": "type(contract Migrations)"}}, "id": 224, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "453:23:1", "typeDescriptions": {"typeIdentifier": "t_contract$_Migrations_$234", "typeString": "contract Migrations"}}, "nodeType": "VariableDeclarationStatement", "src": "431:45:1"}, {"expression": {"argumentTypes": null, "arguments": [{"argumentTypes": null, "id": 229, "name": "last_completed_migration", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 183, "src": "505:24:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "expression": {"argumentTypes": null, "id": 226, "name": "upgraded", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 221, "src": "483:8:1", "typeDescriptions": {"typeIdentifier": "t_contract$_Migrations_$234", "typeString": "contract Migrations"}}, "id": 228, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberName": "setCompleted", "nodeType": "MemberAccess", "referencedDeclaration": 213, "src": "483:21:1", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256) external"}}, "id": 230, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "names": [], "nodeType": "FunctionCall", "src": "483:47:1", "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 231, "nodeType": "ExpressionStatement", "src": "483:47:1"}]}, "documentation": null, "id": 233, "implemented": true, "kind": "function", "modifiers": [{"arguments": null, "id": 218, "modifierName": {"argumentTypes": null, "id": 217, "name": "restricted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 192, "src": "413:10:1", "typeDescriptions": {"typeIdentifier": "t_modifier$__$", "typeString": "modifier ()"}}, "nodeType": "ModifierInvocation", "src": "413:10:1"}], "name": "upgrade", "nodeType": "FunctionDefinition", "parameters": {"id": 216, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 215, "name": "new_address", "nodeType": "VariableDeclaration", "scope": 233, "src": "385:19:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 214, "name": "address", "nodeType": "ElementaryTypeName", "src": "385:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "value": null, "visibility": "internal"}], "src": "384:21:1"}, "returnParameters": {"id": 219, "nodeType": "ParameterList", "parameters": [], "src": "424:0:1"}, "scope": 234, "src": "368:168:1", "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}], "scope": 235, "src": "36:503:1"}], "src": "0:541:1"}, "legacyAST": {"attributes": {"absolutePath": "project:/contracts/Migrations.sol", "exportedSymbols": {"Migrations": [234]}}, "children": [{"attributes": {"literals": ["solidity", ">=", "0.4", ".22", "<", "0.7", ".0"]}, "id": 179, "name": "PragmaDirective", "src": "0:32:1"}, {"attributes": {"baseContracts": [null], "contractDependencies": [null], "contractKind": "contract", "documentation": null, "fullyImplemented": true, "linearizedBaseContracts": [234], "name": "Migrations", "scope": 235}, "children": [{"attributes": {"constant": false, "name": "owner", "scope": 234, "stateVariable": true, "storageLocation": "default", "type": "address", "value": null, "visibility": "public"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 180, "name": "ElementaryTypeName", "src": "61:7:1"}], "id": 181, "name": "VariableDeclaration", "src": "61:20:1"}, {"attributes": {"constant": false, "name": "last_completed_migration", "scope": 234, "stateVariable": true, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "public"}, "children": [{"attributes": {"name": "uint256", "type": "uint256"}, "id": 182, "name": "ElementaryTypeName", "src": "86:7:1"}], "id": 183, "name": "VariableDeclaration", "src": "86:39:1"}, {"attributes": {"documentation": null, "name": "restricted", "visibility": "internal"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 184, "name": "ParameterList", "src": "151:2:1"}, {"children": [{"attributes": {"falseBody": null}, "children": [{"attributes": {"argumentTypes": null, "commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "==", "type": "bool"}, "children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 249, "type": "msg", "value": "msg"}, "id": 185, "name": "Identifier", "src": "165:3:1"}], "id": 186, "name": "MemberAccess", "src": "165:10:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 181, "type": "address", "value": "owner"}, "id": 187, "name": "Identifier", "src": "179:5:1"}], "id": 188, "name": "BinaryOperation", "src": "165:19:1"}, {"id": 189, "name": "PlaceholderStatement", "src": "186:1:1"}], "id": 190, "name": "IfStatement", "src": "161:26:1"}], "id": 191, "name": "Block", "src": "154:39:1"}], "id": 192, "name": "ModifierDefinition", "src": "132:61:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": true, "kind": "constructor", "modifiers": [null], "name": "", "scope": 234, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"attributes": {"parameters": [null]}, "children": [], "id": 193, "name": "ParameterList", "src": "210:2:1"}, {"attributes": {"parameters": [null]}, "children": [], "id": 194, "name": "ParameterList", "src": "220:0:1"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "address"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 181, "type": "address", "value": "owner"}, "id": 195, "name": "Identifier", "src": "227:5:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "sender", "referencedDeclaration": null, "type": "address payable"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 249, "type": "msg", "value": "msg"}, "id": 196, "name": "Identifier", "src": "235:3:1"}], "id": 197, "name": "MemberAccess", "src": "235:10:1"}], "id": 198, "name": "Assignment", "src": "227:18:1"}], "id": 199, "name": "ExpressionStatement", "src": "227:18:1"}], "id": 200, "name": "Block", "src": "220:31:1"}], "id": 201, "name": "FunctionDefinition", "src": "199:52:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "name": "setCompleted", "scope": 234, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "completed", "scope": 213, "stateVariable": false, "storageLocation": "default", "type": "uint256", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "uint", "type": "uint256"}, "id": 202, "name": "ElementaryTypeName", "src": "279:4:1"}], "id": 203, "name": "VariableDeclaration", "src": "279:14:1"}], "id": 204, "name": "ParameterList", "src": "278:16:1"}, {"attributes": {"parameters": [null]}, "children": [], "id": 207, "name": "ParameterList", "src": "313:0:1"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 192, "type": "modifier ()", "value": "restricted"}, "id": 205, "name": "Identifier", "src": "302:10:1"}], "id": 206, "name": "ModifierInvocation", "src": "302:10:1"}, {"children": [{"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "operator": "=", "type": "uint256"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 183, "type": "uint256", "value": "last_completed_migration"}, "id": 208, "name": "Identifier", "src": "320:24:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 203, "type": "uint256", "value": "completed"}, "id": 209, "name": "Identifier", "src": "347:9:1"}], "id": 210, "name": "Assignment", "src": "320:36:1"}], "id": 211, "name": "ExpressionStatement", "src": "320:36:1"}], "id": 212, "name": "Block", "src": "313:49:1"}], "id": 213, "name": "FunctionDefinition", "src": "257:105:1"}, {"attributes": {"documentation": null, "implemented": true, "isConstructor": false, "kind": "function", "name": "upgrade", "scope": 234, "stateMutability": "nonpayable", "superFunction": null, "visibility": "public"}, "children": [{"children": [{"attributes": {"constant": false, "name": "new_address", "scope": 233, "stateVariable": false, "storageLocation": "default", "type": "address", "value": null, "visibility": "internal"}, "children": [{"attributes": {"name": "address", "stateMutability": "nonpayable", "type": "address"}, "id": 214, "name": "ElementaryTypeName", "src": "385:7:1"}], "id": 215, "name": "VariableDeclaration", "src": "385:19:1"}], "id": 216, "name": "ParameterList", "src": "384:21:1"}, {"attributes": {"parameters": [null]}, "children": [], "id": 219, "name": "ParameterList", "src": "424:0:1"}, {"attributes": {"arguments": null}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 192, "type": "modifier ()", "value": "restricted"}, "id": 217, "name": "Identifier", "src": "413:10:1"}], "id": 218, "name": "ModifierInvocation", "src": "413:10:1"}, {"children": [{"attributes": {"assignments": [221]}, "children": [{"attributes": {"constant": false, "name": "upgraded", "scope": 232, "stateVariable": false, "storageLocation": "default", "type": "contract Migrations", "value": null, "visibility": "internal"}, "children": [{"attributes": {"contractScope": null, "name": "Migrations", "referencedDeclaration": 234, "type": "contract Migrations"}, "id": 220, "name": "UserDefinedTypeName", "src": "431:10:1"}], "id": 221, "name": "VariableDeclaration", "src": "431:19:1"}, {"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "contract Migrations", "type_conversion": true}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "overloadedDeclarations": [null], "referencedDeclaration": 234, "type": "type(contract Migrations)", "value": "Migrations"}, "id": 222, "name": "Identifier", "src": "453:10:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 215, "type": "address", "value": "new_address"}, "id": 223, "name": "Identifier", "src": "464:11:1"}], "id": 224, "name": "FunctionCall", "src": "453:23:1"}], "id": 225, "name": "VariableDeclarationStatement", "src": "431:45:1"}, {"children": [{"attributes": {"argumentTypes": null, "isConstant": false, "isLValue": false, "isPure": false, "isStructConstructorCall": false, "lValueRequested": false, "names": [null], "type": "tuple()", "type_conversion": false}, "children": [{"attributes": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "member_name": "setCompleted", "referencedDeclaration": 213, "type": "function (uint256) external"}, "children": [{"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 221, "type": "contract Migrations", "value": "upgraded"}, "id": 226, "name": "Identifier", "src": "483:8:1"}], "id": 228, "name": "MemberAccess", "src": "483:21:1"}, {"attributes": {"argumentTypes": null, "overloadedDeclarations": [null], "referencedDeclaration": 183, "type": "uint256", "value": "last_completed_migration"}, "id": 229, "name": "Identifier", "src": "505:24:1"}], "id": 230, "name": "FunctionCall", "src": "483:47:1"}], "id": 231, "name": "ExpressionStatement", "src": "483:47:1"}], "id": 232, "name": "Block", "src": "424:112:1"}], "id": 233, "name": "FunctionDefinition", "src": "368:168:1"}], "id": 234, "name": "ContractDefinition", "src": "36:503:1"}], "id": 235, "name": "SourceUnit", "src": "0:541:1"}, "compiler": {"name": "solc", "version": "0.5.16+commit.9c3226ce.Emscripten.clang"}, "networks": {"5777": {"events": {}, "links": {}, "address": "******************************************", "transactionHash": "0x051887216a4e30109018c6307b91783fcd65cf4305503bb1495839da1ebd1853"}}, "schemaVersion": "3.4.16", "updatedAt": "2025-05-04T09:56:39.676Z", "networkType": "ethereum", "devdoc": {"methods": {}}, "userdoc": {"methods": {}}}