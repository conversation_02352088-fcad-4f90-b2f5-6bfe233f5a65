import * as faceapi from 'face-api.js';

const MODEL_URL = process.env.PUBLIC_URL + '/models';

export async function loadModels() {
  try {
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
      faceapi.nets.faceRecognitionNet.loadFromUri(MODEL_URL)
    ]);
    console.log('All face detection models loaded successfully');
    return true;
  } catch (error) {
    console.error('Error loading face detection models:', error);
    throw new Error('Không thể tải mô hình nhận dạng khuôn mặt');
  }
}

export async function detectFace(imageElement) {
  if (!imageElement) {
    throw new Error('Không có hình ảnh đầu vào');
  }

  try {
    const detections = await faceapi.detectAllFaces(
      imageElement,
      new faceapi.TinyFaceDetectorOptions()
    ).withFaceDescriptors();
    
    if (!detections || detections.length === 0) {
      throw new Error('Không phát hiện đ<PERSON> khuô<PERSON> mặt');
    }
    
    return detections[0].descriptor;
  } catch (error) {
    console.error('Lỗi phát hiện khuôn mặt:', error);
    throw error;
  }
}