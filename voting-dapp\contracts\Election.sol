// SPDX-License-Identifier: MIT
pragma solidity >=0.4.22 <0.9.0;

contract Election {
    struct Vote {
        uint256 candidateId;
        string photoHash;
        uint256 timestamp;
    }

    struct Candidate {
        uint256 id;
        string name;
        uint256 voteCount;
    }

    // L<PERSON>u thông tin bỏ phiếu của cử tri
    mapping(address => Vote) public voterInfo;
    mapping(address => bool) public voters;
    mapping(uint256 => Candidate) public candidates;
    uint256 public candidateCount;

    // Sự kiện thông báo khi có người bỏ phiếu
    event SuKienBoPhieu(
        uint256 indexed candidateId,
        address indexed voter,
        string photoHash,
        uint256 timestamp
    );

    constructor() public {
        addCandidate("Ứng cử viên 1");
        addCandidate("Ứng cử viên 2");
    }

    // Thêm ứng cử viên mới
    function addCandidate(string memory _name) private {
        candidateCount++;
        candidates[candidateCount] = Candidate(candidateCount, _name, 0);
    }

    // Hàm bỏ phiếu
    function vote(uint256 _candidateId, string memory _photoHash) public {
        // Một địa chỉ chỉ được bỏ phiếu một lần
        require(!voters[msg.sender], "Bạn đã bỏ phiếu trước đó!");

        // Bỏ phiếu cho một ứng cử viên hợp lệ
        require(_candidateId > 0 && _candidateId <= candidateCount, "ID ứng cử viên không hợp lệ!");

        // Yêu cầu ảnh xác thực
        require(bytes(_photoHash).length > 0, "Yêu cầu ảnh xác thực!");

        // Đánh dấu địa chỉ đã bỏ phiếu
        voters[msg.sender] = true;

        // Lưu thông tin bỏ phiếu
        voterInfo[msg.sender] = Vote({
            candidateId: _candidateId,
            photoHash: _photoHash,
            timestamp: block.timestamp
        });

        // Tăng số phiếu bầu của ứng cử viên
        candidates[_candidateId].voteCount++;

        // Kích hoạt sự kiện bỏ phiếu
        emit SuKienBoPhieu(_candidateId, msg.sender, _photoHash, block.timestamp);
    }

    // Hàm lấy thông tin bỏ phiếu của một địa chỉ
    function getVoterInfo(address _voter) public view returns (Vote memory) {
        require(voters[_voter], "Địa chỉ này chưa bỏ phiếu!");
        return voterInfo[_voter];
    }
}