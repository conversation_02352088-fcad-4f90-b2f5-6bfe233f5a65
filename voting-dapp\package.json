{"name": "voting-dapp", "version": "1.0.0", "description": "<div align=\"center\"> <h1>Voting Dapp</h1>", "main": "truffle-config.js", "directories": {"test": "test"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/juliencrn/voting-dapp.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/juliencrn/voting-dapp/issues"}, "homepage": "https://github.com/juliencrn/voting-dapp#readme", "dependencies": {"autoprefixer": "^10.4.21", "chai": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "truffle-assertions": "^0.9.2"}}