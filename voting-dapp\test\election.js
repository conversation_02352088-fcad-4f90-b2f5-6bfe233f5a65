const Election = artifacts.require("Election.sol");
const assert = require("chai").assert;
const truffleAssert = require("truffle-assertions");

contract("Election", (accounts) => {
  it("khởi tạo đúng các ứng cử viên", async () => {
    const election = await Election.deployed();

    const candidate1 = await election.candidates(1);
    assert.equal(candidate1.id, 1, "ID ứng cử viên 1 đúng");
    assert.equal(candidate1.name, "Ứng cử viên 1", "Tên ứng cử viên 1 đúng");
    assert.equal(
      candidate1.voteCount,
      0,
      "<PERSON><PERSON> phiếu bầu của ứng cử viên 1 đúng"
    );

    const candidate2 = await election.candidates(2);
    assert.equal(candidate2.id, 2, "ID ứng cử viên 2 đúng");
    assert.equal(candidate2.name, "Ứng cử viên 2", "Tên ứng cử viên 2 đúng");
    assert.equal(
      candidate2.voteCount,
      0,
      "<PERSON><PERSON> phiếu bầu của ứng cử viên 2 đúng"
    );

    const candidateCount = await election.candidateCount();
    assert.equal(candidateCount, 2, "Số lượng ứng cử viên là 2");
  });

  it("cho phép bỏ phiếu", async () => {
    const election = await Election.deployed();
    const voter = accounts[0];
    const candidateId = 1;

    // Bỏ phiếu
    const _receipt = await election.vote(candidateId, { from: voter });

    const voted = await election.voters(voter);
    assert.equal(voted, true, "Cử tri đã được đánh dấu là đã bỏ phiếu");

    const candidate = await election.candidates(candidateId);
    assert.equal(candidate.voteCount, 1, "Tăng số phiếu bầu của ứng cử viên");
  });

  it("ném lỗi khi bỏ phiếu cho ứng cử viên không hợp lệ", async () => {
    const election = await Election.deployed();

    try {
      const receipt = await election.vote(99, { from: accounts[1] });
      assert.fail(receipt);
    } catch (error) {
      assert(
        error.message.includes("revert"),
        'Thông báo lỗi phải chứa "revert"'
      );
    }

    const candidate1 = await election.candidates(1);
    assert.equal(
      candidate1.voteCount,
      1,
      "Ứng cử viên 1 không nhận thêm phiếu"
    );

    const candidate2 = await election.candidates(2);
    assert.equal(
      candidate2.voteCount,
      0,
      "Ứng cử viên 2 không nhận thêm phiếu"
    );
  });

  it("ném lỗi khi bỏ phiếu hai lần", async () => {
    const election = await Election.deployed();
    const candidateId = 2;
    const voter = accounts[2];

    let receipt = await election.vote(candidateId, { from: voter });
    const candidate = await election.candidates(candidateId);
    assert.equal(candidate.voteCount, 1, "Chấp nhận phiếu bầu đầu tiên");

    try {
      receipt = await election.vote(candidateId, { from: voter });
      assert.fail(receipt);
    } catch (error) {
      assert(
        error.message.includes("revert"),
        'Thông báo lỗi phải chứa "revert"'
      );
    }

    const candidate1 = await election.candidates(1);
    assert.equal(
      candidate1.voteCount,
      1,
      "Ứng cử viên 1 không nhận thêm phiếu"
    );

    const candidate2 = await election.candidates(2);
    assert.equal(
      candidate2.voteCount,
      1,
      "Ứng cử viên 2 không nhận thêm phiếu"
    );
  });

  it("kích hoạt sự kiện khi bỏ phiếu", async () => {
    const election = await Election.deployed();
    const candidateId = 1;
    const voter = accounts[3];

    // Bỏ phiếu
    const receipt = await election.vote(candidateId, { from: voter });

    // Kiểm tra sự kiện được kích hoạt với giá trị đúng
    truffleAssert.eventEmitted(
      receipt,
      "SuKienBoPhieu",
      (event) => event.candidateId == 1
    );
  });
});
